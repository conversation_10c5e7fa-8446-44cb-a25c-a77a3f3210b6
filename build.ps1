#!/usr/bin/env powershell

Write-Host "Building Remote Control..." -ForegroundColor Green

# 检查.NET SDK
try {
    $dotnetVersion = dotnet --version
    Write-Host "Found .NET SDK version: $dotnetVersion" -ForegroundColor Cyan
} catch {
    Write-Host "Error: .NET SDK not found. Please install .NET 6.0 SDK or later." -ForegroundColor Red
    exit 1
}

# 清理输出目录
Write-Host "Cleaning output directory..." -ForegroundColor Yellow
if (Test-Path "build") {
    Remove-Item "build" -Recurse -Force
}
New-Item -ItemType Directory -Path "build" -Force | Out-Null

# 构建项目
Write-Host "Building project..." -ForegroundColor Yellow
try {
    dotnet build RemoteControl.Desktop/RemoteControl.Desktop.csproj -c Release -o build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build successful!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Output files:" -ForegroundColor Cyan
        Get-ChildItem "build" | ForEach-Object { Write-Host "  $($_.Name)" -ForegroundColor White }
        Write-Host ""
        Write-Host "To run the application:" -ForegroundColor Yellow
        Write-Host "  cd build" -ForegroundColor White
        Write-Host "  .\RemoteControl.Desktop.exe" -ForegroundColor White
        Write-Host ""
        Write-Host "Or run directly:" -ForegroundColor Yellow
        Write-Host "  .\build\RemoteControl.Desktop.exe" -ForegroundColor White
    } else {
        Write-Host "Build failed!" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Build failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
