@echo off
chcp 65001 >nul
title KeywordByAny - 无线触控板和键盘

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    KeywordByAny v1.0.0                      ║
echo ║                  无线触控板和键盘控制器                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 正在启动应用程序...
echo.
echo 使用说明：
echo 1. 确保手机和电脑连接到同一WiFi网络
echo 2. 在应用中点击"启动服务器"
echo 3. 点击"生成二维码"用手机扫描连接
echo 4. 或者在手机浏览器中访问显示的IP地址
echo.
echo 按任意键启动应用程序...
pause >nul

cd /d "%~dp0"

if exist "src\KeywordByAny.Desktop\bin\Release\net6.0-windows\KeywordByAny.Desktop.exe" (
    echo 启动开发版本...
    start "" "src\KeywordByAny.Desktop\bin\Release\net6.0-windows\KeywordByAny.Desktop.exe"
) else if exist "build\KeywordByAny.Desktop.exe" (
    echo 启动发布版本...
    start "" "build\KeywordByAny.Desktop.exe"
) else if exist "KeywordByAny.Desktop.exe" (
    echo 启动应用程序...
    start "" "KeywordByAny.Desktop.exe"
) else (
    echo 错误：找不到应用程序文件
    echo 请先运行 build.ps1 构建项目
    echo.
    pause
    exit /b 1
)

echo.
echo 应用程序已启动！
echo 如需关闭此窗口，请直接点击右上角的 X 按钮
echo.
timeout /t 3 >nul
