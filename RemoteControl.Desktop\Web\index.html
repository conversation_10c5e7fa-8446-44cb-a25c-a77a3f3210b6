<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Remote Control - 极客触控板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            background: #0d1117;
            color: #f0f6fc;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            padding: 8px;
            transition: padding-bottom 0.3s ease;
        }

        .container.keyboard-open {
            padding-bottom: 50vh;
        }

        .container.text-input-open {
            padding-bottom: 25vh;
        }

        .header {
            text-align: center;
            padding: 12px 16px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            margin-bottom: 8px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, #00d8ff, transparent);
        }

        .header h2 {
            font-size: 14px;
            font-weight: 600;
            color: #f0f6fc;
            margin: 0;
            letter-spacing: 1px;
        }

        .status {
            font-size: 10px;
            margin-top: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'SF Mono', monospace;
            color: #8b949e;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-right: 6px;
            background: #da3633;
            animation: pulse 2s infinite;
        }

        .status-dot.connected {
            background: #238636;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
                transform: scale(1);
            }

            50% {
                opacity: 0.7;
                transform: scale(1.1);
            }

            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .touchpad {
            flex: 1;
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 12px;
            margin-bottom: 8px;
            position: relative;
            overflow: hidden;
        }

        .touchpad::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 216, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(124, 58, 237, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .touchpad-area {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6e7681;
            font-size: 12px;
            letter-spacing: 0.5px;
            position: relative;
            z-index: 1;
        }

        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
            margin-bottom: 8px;
        }

        .btn {
            height: 48px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            color: #f0f6fc;
            font-size: 12px;
            font-weight: 500;
            font-family: inherit;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .btn:active {
            background: #30363d;
            border-color: #00d8ff;
            transform: scale(0.98);
        }

        .btn.active {
            background: #1f6feb;
            border-color: #1f6feb;
            color: #ffffff;
        }

        .settings-panel {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            display: none;
        }

        .settings-panel.show {
            display: block;
        }

        .setting-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .setting-row label {
            color: #f0f6fc;
            min-width: 80px;
        }

        .setting-row input[type="range"] {
            flex: 1;
            margin: 0 8px;
            background: #30363d;
            height: 4px;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
        }

        .setting-row input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #00d8ff;
            cursor: pointer;
        }

        .setting-row span {
            color: #00d8ff;
            min-width: 30px;
            text-align: right;
            font-weight: bold;
        }

        .shortcuts-panel {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 8px;
            margin-bottom: 8px;
        }

        .bottom-controls {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 6px;
            margin-bottom: 8px;
        }

        .input-toggle,
        .keyboard-toggle,
        .settings-toggle {
            height: 40px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 8px;
            color: #f0f6fc;
            font-size: 10px;
            font-family: inherit;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .input-toggle:active,
        .keyboard-toggle:active,
        .settings-toggle:active {
            background: #30363d;
            transform: scale(0.98);
        }

        .input-toggle.active {
            background: #238636;
            border-color: #238636;
        }

        .keyboard-toggle.active {
            background: #1f6feb;
            border-color: #1f6feb;
        }

        .text-input-panel {
            background: #0d1117;
            border: 1px solid #30363d;
            border-radius: 12px 12px 0 0;
            padding: 8px;
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 25vh;
        }

        .text-input-panel.show {
            display: block;
        }

        .input-mode-toggle {
            display: flex;
            gap: 15px;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .input-mode-toggle label {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #f0f6fc;
            cursor: pointer;
        }

        .input-mode-toggle input[type="radio"] {
            accent-color: #00d8ff;
        }

        .text-input-area {
            width: 100%;
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 8px;
            color: #f0f6fc;
            padding: 8px;
            font-family: inherit;
            font-size: 14px;
            resize: none;
            height: calc(100% - 80px);
            outline: none;
            margin-bottom: 8px;
        }

        .text-input-controls {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .text-input-btn {
            padding: 8px 16px;
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 6px;
            color: #f0f6fc;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .text-input-btn.primary {
            background: #238636;
            border-color: #238636;
        }

        .text-input-btn.danger {
            background: #da3633;
            border-color: #da3633;
        }

        .keyboard {
            background: #0d1117;
            border: 1px solid #30363d;
            border-radius: 12px 12px 0 0;
            padding: 8px;
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 50vh;
            overflow: hidden;
        }

        .keyboard.show {
            display: block;
        }

        .key-row {
            display: flex;
            gap: 3px;
            margin-bottom: 4px;
            justify-content: center;
            flex-wrap: nowrap;
        }

        .key {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 6px;
            color: #f0f6fc;
            padding: 10px 6px;
            flex: 1;
            min-width: 0;
            max-width: 60px;
            min-height: 40px;
            font-size: 12px;
            font-family: inherit;
            cursor: pointer;
            transition: all 0.1s ease;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .key:active {
            background: #1f6feb;
            border-color: #1f6feb;
            transform: scale(0.95);
        }

        .key.space {
            flex: 1;
            min-width: 120px;
        }

        .key.wide {
            flex: 1.5;
            max-width: 80px;
        }

        .key.space {
            flex: 3;
            max-width: none;
        }

        .key.shortcut {
            background: #1f6feb;
            border-color: #1f6feb;
            color: #ffffff;
            font-size: 10px;
            flex: 1;
            max-width: 70px;
        }

        .key.shortcut:active {
            background: #0969da;
            border-color: #0969da;
        }

        .shortcuts {
            margin-top: 4px;
            padding-top: 4px;
            border-top: 1px solid #30363d;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .key {
                font-size: 10px;
                padding: 8px 4px;
                min-height: 35px;
            }

            .key.shortcut {
                font-size: 9px;
            }

            .keyboard {
                padding: 6px;
            }
        }

        @media (min-width: 481px) and (max-width: 768px) {
            .key {
                font-size: 12px;
                padding: 10px 6px;
                min-height: 40px;
            }

            .key.shortcut {
                font-size: 11px;
            }
        }

        @media (min-width: 769px) {
            .key {
                font-size: 14px;
                padding: 12px 8px;
                max-width: 70px;
                min-height: 45px;
            }

            .key.shortcut {
                font-size: 12px;
                max-width: 90px;
            }

            .key.wide {
                max-width: 100px;
            }
        }

        /* 横屏优化 */
        @media (orientation: landscape) and (max-height: 500px) {
            .keyboard {
                max-height: 75vh;
                padding: 3px;
            }

            .key-row {
                margin-bottom: 2px;
            }

            .key {
                padding: 4px 3px;
                font-size: 9px;
            }

            .shortcuts {
                margin-top: 2px;
                padding-top: 2px;
            }
        }

        .touch-feedback {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(0, 216, 255, 0.3);
            pointer-events: none;
            transform: translate(-50%, -50%) scale(0);
            animation: touch-ripple 0.3s ease-out;
        }

        @keyframes touch-ripple {
            0% {
                transform: translate(-50%, -50%) scale(0);
                opacity: 1;
            }

            100% {
                transform: translate(-50%, -50%) scale(2);
                opacity: 0;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h2>REMOTE CONTROL</h2>
            <div class="status" id="status">
                <span class="status-dot" id="statusDot"></span>
                <span id="statusText">CONNECTING...</span>
            </div>
        </div>

        <div class="touchpad" id="touchpad">
            <div class="touchpad-area">
                TOUCHPAD AREA
            </div>
        </div>

        <div class="controls">
            <button class="btn" id="leftClick">L-CLICK</button>
            <button class="btn" id="rightClick">R-CLICK</button>
            <button class="btn" id="scrollMode">SCROLL</button>
        </div>

        <div class="settings-panel" id="settingsPanel">
            <div class="setting-row">
                <label>鼠标灵敏度</label>
                <input type="range" id="mouseSensitivitySlider" min="0.1" max="5.0" step="0.1" value="1.5">
                <span id="mouseSensitivityValue">1.5</span>
            </div>
            <div class="setting-row">
                <label>滚动灵敏度</label>
                <input type="range" id="scrollSensitivitySlider" min="0.1" max="5.0" step="0.1" value="3.0">
                <span id="scrollSensitivityValue">3.0</span>
            </div>
        </div>

        <!-- 快捷键面板 -->
        <div class="shortcuts-panel" id="shortcutsPanel">
            <div class="key-row shortcuts">
                <button class="key shortcut" data-shortcut="ctrl+c">复制</button>
                <button class="key shortcut" data-shortcut="ctrl+v">粘贴</button>
                <button class="key shortcut" data-shortcut="ctrl+z">撤销</button>
                <button class="key shortcut" data-shortcut="ctrl+f">查找</button>
                <button class="key shortcut" data-shortcut="ctrl+n">新建</button>
            </div>

            <div class="key-row shortcuts">
                <button class="key shortcut" data-shortcut="alt+tab">切换</button>
                <button class="key shortcut" data-shortcut="alt+space">菜单</button>
                <button class="key shortcut" data-shortcut="ctrl+s">保存</button>
                <button class="key shortcut" data-shortcut="ctrl+a">全选</button>
                <button class="key" onclick="showCustomShortcut()">➕ 自定义</button>
            </div>
        </div>

        <div class="bottom-controls">
            <button class="input-toggle" id="inputToggle">📝 文本输入</button>
            <button class="keyboard-toggle" id="keyboardToggle">⌨️ 虚拟键盘</button>
            <button class="settings-toggle" id="settingsToggle">⚙️ 设置</button>
        </div>

        <!-- 文本输入面板 -->
        <div class="text-input-panel" id="textInputPanel">
            <div class="input-mode-toggle">
                <label>
                    <input type="radio" name="inputMode" value="batch" checked> 批量模式
                </label>
                <label>
                    <input type="radio" name="inputMode" value="realtime"> 实时模式
                </label>
            </div>
            <textarea class="text-input-area" id="textInputArea"
                placeholder="批量模式：输入完成后点击发送&#10;实时模式：输入时直接发送到电脑"></textarea>
            <div class="text-input-controls">
                <button class="text-input-btn danger" onclick="clearTextInput()">清空</button>
                <button class="text-input-btn" onclick="closeTextInput()">关闭</button>
                <button class="text-input-btn primary" id="sendTextBtn" onclick="sendTextInput()">发送</button>
            </div>
        </div>

        <div class="keyboard" id="keyboard">
            <!-- 数字行 -->
            <div class="key-row">
                <button class="key" data-key="1">1</button>
                <button class="key" data-key="2">2</button>
                <button class="key" data-key="3">3</button>
                <button class="key" data-key="4">4</button>
                <button class="key" data-key="5">5</button>
                <button class="key" data-key="6">6</button>
                <button class="key" data-key="7">7</button>
                <button class="key" data-key="8">8</button>
                <button class="key" data-key="9">9</button>
                <button class="key" data-key="0">0</button>
            </div>

            <!-- 字母行1 -->
            <div class="key-row">
                <button class="key" data-key="q">Q</button>
                <button class="key" data-key="w">W</button>
                <button class="key" data-key="e">E</button>
                <button class="key" data-key="r">R</button>
                <button class="key" data-key="t">T</button>
                <button class="key" data-key="y">Y</button>
                <button class="key" data-key="u">U</button>
                <button class="key" data-key="i">I</button>
                <button class="key" data-key="o">O</button>
                <button class="key" data-key="p">P</button>
            </div>

            <!-- 字母行2 -->
            <div class="key-row">
                <button class="key" data-key="a">A</button>
                <button class="key" data-key="s">S</button>
                <button class="key" data-key="d">D</button>
                <button class="key" data-key="f">F</button>
                <button class="key" data-key="g">G</button>
                <button class="key" data-key="h">H</button>
                <button class="key" data-key="j">J</button>
                <button class="key" data-key="k">K</button>
                <button class="key" data-key="l">L</button>
            </div>

            <!-- 字母行3 + 退格 -->
            <div class="key-row">
                <button class="key" data-key="z">Z</button>
                <button class="key" data-key="x">X</button>
                <button class="key" data-key="c">C</button>
                <button class="key" data-key="v">V</button>
                <button class="key" data-key="b">B</button>
                <button class="key" data-key="n">N</button>
                <button class="key" data-key="m">M</button>
                <button class="key wide" data-key="Backspace">⌫</button>
            </div>

            <!-- 功能键行 -->
            <div class="key-row">
                <button class="key" data-key="Tab">TAB</button>
                <button class="key space" data-key=" ">SPACE</button>
                <button class="key" data-key="Enter">↵</button>
                <button class="key" data-key="Escape">ESC</button>
            </div>

            <!-- 方向键 + 删除 -->
            <div class="key-row">
                <button class="key" data-key="ArrowUp">↑</button>
                <button class="key" data-key="ArrowDown">↓</button>
                <button class="key" data-key="ArrowLeft">←</button>
                <button class="key" data-key="ArrowRight">→</button>
                <button class="key" data-key="Delete">DEL</button>
                <button class="key" data-key="Home">HOME</button>
                <button class="key" data-key="End">END</button>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let lastTouch = {
            x: 0,
            y: 0
        };
        let isScrollMode = false;
        let mouseSensitivity = 1.5;
        let scrollSensitivity = 3.0;
        let isDragging = false;
        let dragStartTime = 0;

        // 连接WebSocket
        function connect() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}`;

            console.log('Connecting to:', wsUrl);
            ws = new WebSocket(wsUrl);

            ws.onopen = function () {
                console.log('WebSocket connected successfully');
                isConnected = true;
                updateStatus('CONNECTED', 'connected');
            };

            ws.onclose = function (event) {
                console.log('WebSocket closed:', event.code, event.reason);
                isConnected = false;
                updateStatus('DISCONNECTED', 'disconnected');
                setTimeout(connect, 2000);
            };

            ws.onerror = function (error) {
                console.error('WebSocket error:', error);
                isConnected = false;
                updateStatus('ERROR', 'disconnected');
            };


        }

        function updateStatus(text, className) {
            const statusText = document.getElementById('statusText');
            const statusDot = document.getElementById('statusDot');
            statusText.textContent = text;
            statusDot.className = `status-dot ${className}`;
        }

        function sendMessage(data) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(data));
            }
        }

        // 触控板事件处理
        const touchpad = document.getElementById('touchpad');
        let touchStartTime = 0;
        let touchCount = 0;
        let lastTouchTime = 0;
        let touchTimeout = null;
        let hasMoved = false;

        touchpad.addEventListener('touchstart', function (e) {
            e.preventDefault();
            const touch = e.touches[0];

            lastTouch.x = touch.clientX;
            lastTouch.y = touch.clientY;
            touchStartTime = Date.now();
            hasMoved = false;

            // 双击检测
            const currentTime = Date.now();
            if (currentTime - lastTouchTime < 300) {
                touchCount++;
                if (touchCount === 2) {
                    // 双击 = 开始拖拽模式
                    isDragging = true;
                    dragStartTime = currentTime;
                    sendMessage({
                        type: 'dragStart'
                    });
                    touchCount = 0;
                    clearTimeout(touchTimeout);
                    console.log('拖拽模式开始');
                    return;
                }
            } else {
                touchCount = 1;
            }
            lastTouchTime = currentTime;

            // 两指触控检测
            if (e.touches.length === 2) {
                isScrollMode = true;
                document.getElementById('scrollMode').classList.add('active');
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                lastTouch.x = (touch1.clientX + touch2.clientX) / 2;
                lastTouch.y = (touch1.clientY + touch2.clientY) / 2;
            }
        });

        touchpad.addEventListener('touchmove', function (e) {
            e.preventDefault();
            hasMoved = true;

            if (e.touches.length === 1) {
                // 单指移动
                const touch = e.touches[0];
                const deltaX = touch.clientX - lastTouch.x;
                const deltaY = touch.clientY - lastTouch.y;

                const adjustedDeltaX = deltaX * mouseSensitivity;
                const adjustedDeltaY = deltaY * mouseSensitivity;

                if (isDragging) {
                    // 拖拽模式 = 鼠标拖拽
                    console.log(`Dragging: (${adjustedDeltaX}, ${adjustedDeltaY})`);
                    sendMessage({
                        type: 'drag',
                        deltaX: adjustedDeltaX,
                        deltaY: adjustedDeltaY
                    });
                } else {
                    // 普通移动 = 鼠标移动
                    console.log(
                        `Mouse move: original(${deltaX}, ${deltaY}) -> adjusted(${adjustedDeltaX}, ${adjustedDeltaY}) sensitivity=${mouseSensitivity}`
                    );
                    sendMessage({
                        type: 'move',
                        deltaX: adjustedDeltaX,
                        deltaY: adjustedDeltaY
                    });
                }

                lastTouch.x = touch.clientX;
                lastTouch.y = touch.clientY;
            } else if (e.touches.length === 2) {
                // 两指滑动 = 滚动
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                const centerX = (touch1.clientX + touch2.clientX) / 2;
                const centerY = (touch1.clientY + touch2.clientY) / 2;

                const deltaX = centerX - lastTouch.x;
                const deltaY = centerY - lastTouch.y;

                const adjustedScrollX = deltaX * scrollSensitivity;
                const adjustedScrollY = deltaY * scrollSensitivity;

                console.log(
                    `Scroll: original(${deltaX}, ${deltaY}) -> adjusted(${adjustedScrollX}, ${adjustedScrollY}) sensitivity=${scrollSensitivity}`
                );

                sendMessage({
                    type: 'scroll',
                    deltaX: adjustedScrollX,
                    deltaY: adjustedScrollY
                });

                lastTouch.x = centerX;
                lastTouch.y = centerY;
            }
        });

        touchpad.addEventListener('touchend', function (e) {
            e.preventDefault();

            if (e.touches.length === 0) {
                const touchDuration = Date.now() - touchStartTime;

                if (isDragging) {
                    // 结束拖拽
                    isDragging = false;
                    sendMessage({
                        type: 'dragEnd'
                    });
                    console.log('拖拽模式结束');
                } else if (touchDuration < 200 && !hasMoved && touchCount === 1) {
                    // 单击检测（短时间触摸且没有移动太多）
                    clearTimeout(touchTimeout);
                    touchTimeout = setTimeout(() => {
                        if (touchCount === 1) {
                            // 单击 = 左键
                            sendMessage({
                                type: 'click',
                                button: 'left'
                            });
                        }
                        touchCount = 0;
                    }, 250);
                }

                isScrollMode = false;
                document.getElementById('scrollMode').classList.remove('active');
            }
        });

        // 按钮事件
        document.getElementById('leftClick').addEventListener('click', function () {
            sendMessage({
                type: 'click',
                button: 'left'
            });
        });

        document.getElementById('rightClick').addEventListener('click', function () {
            sendMessage({
                type: 'click',
                button: 'right'
            });
        });

        document.getElementById('scrollMode').addEventListener('click', function () {
            isScrollMode = !isScrollMode;
            this.classList.toggle('active', isScrollMode);
        });

        // 设置面板切换
        document.getElementById('settingsToggle').addEventListener('click', function () {
            const settingsPanel = document.getElementById('settingsPanel');
            const isVisible = settingsPanel.classList.contains('show');

            if (isVisible) {
                settingsPanel.classList.remove('show');
                this.textContent = '设置';
            } else {
                settingsPanel.classList.add('show');
                this.textContent = '隐藏设置';
            }
        });

        // 文本输入切换
        document.getElementById('inputToggle').addEventListener('click', function () {
            const textInputPanel = document.getElementById('textInputPanel');
            const keyboard = document.getElementById('keyboard');
            const container = document.querySelector('.container');
            const keyboardToggle = document.getElementById('keyboardToggle');

            const isVisible = textInputPanel.classList.contains('show');

            if (isVisible) {
                // 关闭文本输入
                textInputPanel.classList.remove('show');
                container.classList.remove('text-input-open');
                this.classList.remove('active');
                this.textContent = '📝 文本输入';
            } else {
                // 关闭键盘（如果打开）
                keyboard.classList.remove('show');
                container.classList.remove('keyboard-open');
                keyboardToggle.classList.remove('active');
                keyboardToggle.textContent = '⌨️ 虚拟键盘';

                // 打开文本输入
                textInputPanel.classList.add('show');
                container.classList.add('text-input-open');
                this.classList.add('active');
                this.textContent = '📝 关闭输入';

                // 聚焦到文本框
                setTimeout(() => {
                    document.getElementById('textInputArea').focus();
                }, 100);
            }
        });

        // 键盘切换
        document.getElementById('keyboardToggle').addEventListener('click', function () {
            const keyboard = document.getElementById('keyboard');
            const textInputPanel = document.getElementById('textInputPanel');
            const container = document.querySelector('.container');
            const inputToggle = document.getElementById('inputToggle');

            const isVisible = keyboard.classList.contains('show');

            if (isVisible) {
                // 关闭键盘
                keyboard.classList.remove('show');
                container.classList.remove('keyboard-open');
                this.classList.remove('active');
                this.textContent = '⌨️ 虚拟键盘';
            } else {
                // 关闭文本输入（如果打开）
                textInputPanel.classList.remove('show');
                container.classList.remove('text-input-open');
                inputToggle.classList.remove('active');
                inputToggle.textContent = '📝 文本输入';

                // 打开键盘
                keyboard.classList.add('show');
                container.classList.add('keyboard-open');
                this.classList.add('active');
                this.textContent = '⌨️ 关闭键盘';
            }
        });

        // 灵敏度滑块事件
        document.getElementById('mouseSensitivitySlider').addEventListener('input', function () {
            mouseSensitivity = parseFloat(this.value);
            document.getElementById('mouseSensitivityValue').textContent = this.value;
            console.log('Mouse sensitivity updated:', mouseSensitivity);
        });

        document.getElementById('scrollSensitivitySlider').addEventListener('input', function () {
            scrollSensitivity = parseFloat(this.value);
            document.getElementById('scrollSensitivityValue').textContent = this.value;
            console.log('Scroll sensitivity updated:', scrollSensitivity);
        });



        // 自定义快捷键功能
        function showCustomShortcut() {
            const shortcut = prompt('输入自定义快捷键 (例如: ctrl+shift+t, alt+f4):');
            if (shortcut && shortcut.trim()) {
                sendMessage({
                    type: 'shortcut',
                    keys: shortcut.trim().toLowerCase()
                });
            }
        }

        // 发送快捷键
        function sendShortcut(keys) {
            console.log('发送快捷键:', keys);
            alert('发送快捷键: ' + keys); // 添加弹窗调试
            sendMessage({
                type: 'shortcut',
                keys: keys
            });
        }

        // 文本输入相关函数
        function sendTextInput() {
            const textArea = document.getElementById('textInputArea');
            const text = textArea.value;

            if (text.trim()) {
                // 批量发送整个文本，让服务器端处理
                sendMessage({
                    type: 'bulkText',
                    text: text
                });
                textArea.value = '';
                console.log('Bulk text sent:', text);
            }
        }

        function clearTextInput() {
            document.getElementById('textInputArea').value = '';
        }

        function closeTextInput() {
            const textInputPanel = document.getElementById('textInputPanel');
            const container = document.querySelector('.container');
            const inputToggle = document.getElementById('inputToggle');

            textInputPanel.classList.remove('show');
            container.classList.remove('text-input-open');
            inputToggle.classList.remove('active');
            inputToggle.textContent = '📝 文本输入';
        }

        // 输入模式相关
        let currentInputMode = 'batch';
        let lastInputValue = '';
        let inputTimeout = null;

        // 文本框事件和输入模式切换
        document.addEventListener('DOMContentLoaded', function () {
            const textArea = document.getElementById('textInputArea');
            const sendBtn = document.getElementById('sendTextBtn');

            // 输入模式切换
            document.querySelectorAll('input[name="inputMode"]').forEach(radio => {
                radio.addEventListener('change', function () {
                    currentInputMode = this.value;
                    if (currentInputMode === 'realtime') {
                        textArea.placeholder = '实时模式：输入时直接发送到电脑';
                        sendBtn.style.display = 'none';
                        lastInputValue = textArea.value;
                    } else {
                        textArea.placeholder = '批量模式：输入完成后点击发送';
                        sendBtn.style.display = 'block';
                    }
                    console.log('输入模式切换为:', currentInputMode);
                });
            });

            // 实时输入处理
            textArea.addEventListener('input', function (e) {
                if (currentInputMode === 'realtime') {
                    clearTimeout(inputTimeout);
                    inputTimeout = setTimeout(() => {
                        handleRealtimeInput();
                    }, 100); // 100ms防抖
                }
            });

            // Ctrl+Enter 快速发送
            textArea.addEventListener('keydown', function (e) {
                if (e.key === 'Enter' && e.ctrlKey) {
                    e.preventDefault();
                    if (currentInputMode === 'batch') {
                        sendTextInput();
                    }
                }
            });
        });

        // 处理实时输入
        function handleRealtimeInput() {
            const textArea = document.getElementById('textInputArea');
            const currentValue = textArea.value;

            if (currentValue !== lastInputValue) {
                // 计算差异并发送
                const diff = calculateTextDiff(lastInputValue, currentValue);
                if (diff.operations.length > 0) {
                    sendMessage({
                        type: 'textDiff',
                        operations: diff.operations
                    });
                    console.log('实时输入差异:', diff.operations);
                }
                lastInputValue = currentValue;
            }
        }

        // 计算文本差异
        function calculateTextDiff(oldText, newText) {
            const operations = [];

            if (newText.length > oldText.length) {
                // 有新字符添加
                const addedText = newText.slice(oldText.length);
                operations.push({
                    type: 'add',
                    text: addedText
                });
            } else if (newText.length < oldText.length) {
                // 有字符删除
                const deletedCount = oldText.length - newText.length;
                operations.push({
                    type: 'delete',
                    count: deletedCount
                });
            } else if (newText !== oldText) {
                // 字符替换（先删除再添加）
                operations.push({
                    type: 'delete',
                    count: oldText.length
                });
                operations.push({
                    type: 'add',
                    text: newText
                });
            }

            return {
                operations
            };
        }

        // 键盘按键事件
        document.querySelectorAll('.key').forEach(key => {
            key.addEventListener('click', function () {
                const keyValue = this.getAttribute('data-key');
                const shortcut = this.getAttribute('data-shortcut');

                if (shortcut) {
                    // 发送快捷键
                    sendShortcut(shortcut);
                } else if (keyValue) {
                    // 发送普通按键
                    sendMessage({
                        type: 'key',
                        key: keyValue
                    });
                }
            });
        });

        // 启动连接
        connect();
    </script>
</body>

</html>