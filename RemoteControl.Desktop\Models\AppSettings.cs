using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace RemoteControl.Desktop.Models
{
    public class AppSettings : INotifyPropertyChanged
    {
        private int _port = 3000;
        private bool _autoStart = true;
        private bool _startWithWindows = false;
        private bool _minimizeToTray = true;
        private double _mouseSensitivity = 1.0;
        private double _scrollSensitivity = 1.0;

        public int Port
        {
            get => _port;
            set
            {
                if (_port != value)
                {
                    _port = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool AutoStart
        {
            get => _autoStart;
            set
            {
                if (_autoStart != value)
                {
                    _autoStart = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool StartWithWindows
        {
            get => _startWithWindows;
            set
            {
                if (_startWithWindows != value)
                {
                    _startWithWindows = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool MinimizeToTray
        {
            get => _minimizeToTray;
            set
            {
                if (_minimizeToTray != value)
                {
                    _minimizeToTray = value;
                    OnPropertyChanged();
                }
            }
        }

        public double MouseSensitivity
        {
            get => _mouseSensitivity;
            set
            {
                if (Math.Abs(_mouseSensitivity - value) > 0.01)
                {
                    _mouseSensitivity = Math.Max(0.1, Math.Min(5.0, value));
                    OnPropertyChanged();
                }
            }
        }

        public double ScrollSensitivity
        {
            get => _scrollSensitivity;
            set
            {
                if (Math.Abs(_scrollSensitivity - value) > 0.01)
                {
                    _scrollSensitivity = Math.Max(0.1, Math.Min(5.0, value));
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
