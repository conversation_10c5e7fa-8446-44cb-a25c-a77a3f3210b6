<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Remote Control 连接测试</h1>
        
        <div class="status info">
            <strong>当前页面地址:</strong> <span id="currentUrl"></span>
        </div>
        
        <div class="status" id="connectionStatus">
            正在检测连接...
        </div>
        
        <h3>手动测试</h3>
        <p>服务器地址: <input type="text" id="serverUrl" placeholder="http://*************:8080"></p>
        <button onclick="testConnection()">测试连接</button>
        <button onclick="testWebSocket()">测试WebSocket</button>
        
        <h3>网络信息</h3>
        <div id="networkInfo"></div>
        
        <h3>连接日志</h3>
        <div id="log" style="background: #f8f9fa; padding: 10px; border-radius: 5px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('connectionStatus');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        // 显示当前URL
        document.getElementById('currentUrl').textContent = window.location.href;
        
        // 显示网络信息
        function showNetworkInfo() {
            const info = document.getElementById('networkInfo');
            info.innerHTML = `
                <p><strong>User Agent:</strong> ${navigator.userAgent}</p>
                <p><strong>语言:</strong> ${navigator.language}</p>
                <p><strong>在线状态:</strong> ${navigator.onLine ? '在线' : '离线'}</p>
                <p><strong>协议:</strong> ${window.location.protocol}</p>
                <p><strong>主机:</strong> ${window.location.host}</p>
            `;
        }
        
        // 测试HTTP连接
        async function testConnection() {
            const url = document.getElementById('serverUrl').value || window.location.origin;
            log(`测试HTTP连接到: ${url}`);
            
            try {
                const response = await fetch(url);
                if (response.ok) {
                    log(`✓ HTTP连接成功 (状态: ${response.status})`);
                    updateStatus('HTTP连接正常', 'success');
                } else {
                    log(`✗ HTTP连接失败 (状态: ${response.status})`);
                    updateStatus('HTTP连接失败', 'error');
                }
            } catch (error) {
                log(`✗ HTTP连接错误: ${error.message}`);
                updateStatus('HTTP连接错误', 'error');
            }
        }
        
        // 测试WebSocket连接
        function testWebSocket() {
            const url = document.getElementById('serverUrl').value || window.location.origin;
            const wsUrl = url.replace('http://', 'ws://').replace('https://', 'wss://');
            
            log(`测试WebSocket连接到: ${wsUrl}`);
            
            const ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                log('✓ WebSocket连接成功');
                updateStatus('WebSocket连接正常', 'success');
                ws.close();
            };
            
            ws.onerror = function(error) {
                log(`✗ WebSocket连接错误: ${error}`);
                updateStatus('WebSocket连接失败', 'error');
            };
            
            ws.onclose = function(event) {
                log(`WebSocket连接关闭 (代码: ${event.code}, 原因: ${event.reason})`);
            };
            
            // 5秒后超时
            setTimeout(() => {
                if (ws.readyState === WebSocket.CONNECTING) {
                    log('✗ WebSocket连接超时');
                    updateStatus('WebSocket连接超时', 'error');
                    ws.close();
                }
            }, 5000);
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            showNetworkInfo();
            log('页面加载完成，开始自动测试...');
            
            // 自动测试HTTP连接
            setTimeout(testConnection, 1000);
            
            // 自动测试WebSocket连接
            setTimeout(testWebSocket, 2000);
        };
    </script>
</body>
</html>
