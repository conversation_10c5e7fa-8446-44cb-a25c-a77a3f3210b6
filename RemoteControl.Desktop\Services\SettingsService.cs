using System;
using System.IO;
using System.Text.Json;
using Microsoft.Win32;
using RemoteControl.Desktop.Models;

namespace RemoteControl.Desktop.Services
{
    public class SettingsService
    {
        private readonly string _settingsPath;
        private AppSettings _settings;

        public AppSettings Settings => _settings;

        public event Action<AppSettings> SettingsChanged;

        public SettingsService()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var appFolder = Path.Combine(appDataPath, "RemoteControl");
            Directory.CreateDirectory(appFolder);
            _settingsPath = Path.Combine(appFolder, "settings.json");

            LoadSettings();
            _settings.PropertyChanged += (s, e) => SettingsChanged?.Invoke(_settings);
        }

        public void LoadSettings()
        {
            try
            {
                if (File.Exists(_settingsPath))
                {
                    var json = File.ReadAllText(_settingsPath);
                    _settings = JsonSerializer.Deserialize<AppSettings>(json) ?? new AppSettings();
                }
                else
                {
                    _settings = new AppSettings();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载设置失败: {ex.Message}");
                _settings = new AppSettings();
            }
        }

        public void SaveSettings()
        {
            try
            {
                var json = JsonSerializer.Serialize(_settings, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                File.WriteAllText(_settingsPath, json);

                // 更新开机自启动设置
                UpdateStartupRegistry();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存设置失败: {ex.Message}");
            }
        }

        private void UpdateStartupRegistry()
        {
            try
            {
                const string keyName = @"SOFTWARE\Microsoft\Windows\CurrentVersion\Run";
                const string appName = "RemoteControl";

                using var key = Registry.CurrentUser.OpenSubKey(keyName, true);
                if (key != null)
                {
                    if (_settings.StartWithWindows)
                    {
                        var exePath = System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName;
                        key.SetValue(appName, $"\"{exePath}\"");
                    }
                    else
                    {
                        key.DeleteValue(appName, false);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新开机自启动设置失败: {ex.Message}");
            }
        }

        public void ResetToDefaults()
        {
            _settings = new AppSettings();
            SaveSettings();
        }
    }
}
