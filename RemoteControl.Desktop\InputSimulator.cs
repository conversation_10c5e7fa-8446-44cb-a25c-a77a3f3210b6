using System;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace RemoteControl.Desktop
{
    public static class InputSimulator
    {
        // Windows API 常量
        private const int MOUSEEVENTF_MOVE = 0x0001;
        private const int MOUSEEVENTF_LEFTDOWN = 0x0002;
        private const int MOUSEEVENTF_LEFTUP = 0x0004;
        private const int MOUSEEVENTF_RIGHTDOWN = 0x0008;
        private const int MOUSEEVENTF_RIGHTUP = 0x0010;
        private const int MOUSEEVENTF_WHEEL = 0x0800;

        // Windows API 导入
        [DllImport("user32.dll")]
        private static extern void mouse_event(int dwFlags, int dx, int dy, int dwData, int dwExtraInfo);

        [DllImport("user32.dll")]
        private static extern bool SetCursorPos(int x, int y);

        [DllImport("user32.dll")]
        private static extern bool GetCursorPos(out POINT lpPoint);

        [DllImport("user32.dll")]
        private static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, int dwExtraInfo);

        [StructLayout(LayoutKind.Sequential)]
        public struct POINT
        {
            public int X;
            public int Y;
        }

        private const uint KEYEVENTF_KEYUP = 0x0002;

        /// <summary>
        /// 移动鼠标
        /// </summary>
        public static void MoveMouse(int deltaX, int deltaY)
        {
            try
            {
                GetCursorPos(out POINT currentPos);
                int newX = currentPos.X + deltaX;
                int newY = currentPos.Y + deltaY;
                
                // 确保坐标在屏幕范围内
                newX = Math.Max(0, Math.Min(Screen.PrimaryScreen.Bounds.Width - 1, newX));
                newY = Math.Max(0, Math.Min(Screen.PrimaryScreen.Bounds.Height - 1, newY));
                
                SetCursorPos(newX, newY);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"移动鼠标失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 左键点击
        /// </summary>
        public static void LeftClick()
        {
            try
            {
                mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
                System.Threading.Thread.Sleep(10);
                mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"左键点击失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 右键点击
        /// </summary>
        public static void RightClick()
        {
            try
            {
                mouse_event(MOUSEEVENTF_RIGHTDOWN, 0, 0, 0, 0);
                System.Threading.Thread.Sleep(10);
                mouse_event(MOUSEEVENTF_RIGHTUP, 0, 0, 0, 0);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"右键点击失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 滚轮滚动
        /// </summary>
        public static void Scroll(int delta)
        {
            try
            {
                // delta 正值向上滚动，负值向下滚动
                mouse_event(MOUSEEVENTF_WHEEL, 0, 0, delta * 120, 0);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"滚轮滚动失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送按键
        /// </summary>
        public static void SendKey(string key)
        {
            try
            {
                if (string.IsNullOrEmpty(key)) return;

                // 处理特殊按键
                switch (key.ToLower())
                {
                    case "backspace":
                        SendKeyCode(Keys.Back);
                        break;
                    case "enter":
                        SendKeyCode(Keys.Enter);
                        break;
                    case "tab":
                        SendKeyCode(Keys.Tab);
                        break;
                    case "escape":
                        SendKeyCode(Keys.Escape);
                        break;
                    case "space":
                    case " ":
                        SendKeyCode(Keys.Space);
                        break;
                    case "delete":
                        SendKeyCode(Keys.Delete);
                        break;
                    case "home":
                        SendKeyCode(Keys.Home);
                        break;
                    case "end":
                        SendKeyCode(Keys.End);
                        break;
                    case "pageup":
                        SendKeyCode(Keys.PageUp);
                        break;
                    case "pagedown":
                        SendKeyCode(Keys.PageDown);
                        break;
                    case "arrowup":
                        SendKeyCode(Keys.Up);
                        break;
                    case "arrowdown":
                        SendKeyCode(Keys.Down);
                        break;
                    case "arrowleft":
                        SendKeyCode(Keys.Left);
                        break;
                    case "arrowright":
                        SendKeyCode(Keys.Right);
                        break;
                    default:
                        // 处理普通字符
                        if (key.Length == 1)
                        {
                            char c = key[0];
                            if (char.IsLetter(c))
                            {
                                // 字母
                                Keys keyCode = (Keys)Enum.Parse(typeof(Keys), c.ToString().ToUpper());
                                SendKeyCode(keyCode);
                            }
                            else if (char.IsDigit(c))
                            {
                                // 数字
                                Keys keyCode = (Keys)Enum.Parse(typeof(Keys), "D" + c);
                                SendKeyCode(keyCode);
                            }
                            else
                            {
                                // 其他字符，使用SendKeys
                                SendKeys.SendWait(key);
                            }
                        }
                        else
                        {
                            // 多字符字符串，使用SendKeys
                            SendKeys.SendWait(key);
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发送按键失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送按键代码
        /// </summary>
        private static void SendKeyCode(Keys key)
        {
            try
            {
                byte vkCode = (byte)key;
                keybd_event(vkCode, 0, 0, 0); // 按下
                System.Threading.Thread.Sleep(10);
                keybd_event(vkCode, 0, KEYEVENTF_KEYUP, 0); // 释放
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发送按键代码失败: {ex.Message}");
            }
        }
    }
}
