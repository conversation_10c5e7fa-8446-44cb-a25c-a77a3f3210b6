using System;
using System.IO;
using System.Net;
using System.Net.WebSockets;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using RemoteControl.Desktop.Models;

namespace RemoteControl.Desktop
{
    public class SimpleServer
    {
        private HttpListener _httpListener;
        private bool _isRunning = false;
        private readonly object _lock = new object();
        private readonly AppSettings _settings;

        public event Action<string> OnLog;
        public event Action OnClientConnected;
        public event Action OnClientDisconnected;

        public bool IsRunning
        {
            get
            {
                lock (_lock)
                {
                    return _isRunning;
                }
            }
        }

        public SimpleServer(AppSettings settings)
        {
            _settings = settings;
        }

        public void Start()
        {
            lock (_lock)
            {
                if (_isRunning) return;

                try
                {
                    _httpListener = new HttpListener();
                    _httpListener.Prefixes.Add($"http://+:{_settings.Port}/");
                    _httpListener.Start();
                    _isRunning = true;

                    OnLog?.Invoke($"服务器已启动，端口: {_settings.Port}");

                    // 启动处理请求的任务
                    Task.Run(HandleRequests);
                }
                catch (Exception ex)
                {
                    OnLog?.Invoke($"启动服务器失败: {ex.Message}");
                    throw;
                }
            }
        }

        public void Stop()
        {
            lock (_lock)
            {
                if (!_isRunning) return;

                try
                {
                    _isRunning = false;
                    _httpListener?.Stop();
                    _httpListener?.Close();
                    _httpListener = null;

                    OnLog?.Invoke("服务器已停止");
                }
                catch (Exception ex)
                {
                    OnLog?.Invoke($"停止服务器时出错: {ex.Message}");
                }
            }
        }

        private async Task HandleRequests()
        {
            while (IsRunning)
            {
                try
                {
                    var context = await _httpListener.GetContextAsync();
                    _ = Task.Run(() => ProcessRequest(context));
                }
                catch (ObjectDisposedException)
                {
                    break;
                }
                catch (HttpListenerException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    OnLog?.Invoke($"处理请求时出错: {ex.Message}");
                }
            }
        }

        private async Task ProcessRequest(HttpListenerContext context)
        {
            try
            {
                var request = context.Request;
                var response = context.Response;

                if (request.IsWebSocketRequest)
                {
                    await HandleWebSocketRequest(context);
                }
                else
                {
                    await HandleHttpRequest(context);
                }
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"处理请求失败: {ex.Message}");
            }
        }

        private async Task HandleWebSocketRequest(HttpListenerContext context)
        {
            try
            {
                var wsContext = await context.AcceptWebSocketAsync(null);
                var webSocket = wsContext.WebSocket;

                OnLog?.Invoke("WebSocket客户端已连接");
                OnClientConnected?.Invoke();

                await HandleWebSocketCommunication(webSocket);

                OnLog?.Invoke("WebSocket客户端已断开");
                OnClientDisconnected?.Invoke();
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"WebSocket处理失败: {ex.Message}");
            }
        }

        private async Task HandleWebSocketCommunication(WebSocket webSocket)
        {
            var buffer = new byte[1024 * 4];

            while (webSocket.State == WebSocketState.Open)
            {
                try
                {
                    var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);

                    if (result.MessageType == WebSocketMessageType.Text)
                    {
                        var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                        await ProcessWebSocketMessage(message);
                    }
                    else if (result.MessageType == WebSocketMessageType.Close)
                    {
                        await webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "", CancellationToken.None);
                        break;
                    }
                }
                catch (WebSocketException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    OnLog?.Invoke($"WebSocket通信错误: {ex.Message}");
                    break;
                }
            }
        }

        private async Task ProcessWebSocketMessage(string message)
        {
            try
            {
                var data = JsonConvert.DeserializeObject<dynamic>(message);
                string type = data.type;

                switch (type)
                {
                    case "move":
                        double deltaX = (double)data.deltaX * _settings.MouseSensitivity;
                        double deltaY = (double)data.deltaY * _settings.MouseSensitivity;
                        InputSimulator.MoveMouse((int)deltaX, (int)deltaY);
                        break;

                    case "click":
                        string button = data.button;
                        if (button == "left")
                            InputSimulator.LeftClick();
                        else if (button == "right")
                            InputSimulator.RightClick();
                        break;

                    case "scroll":
                        double scrollX = (double)data.deltaX * _settings.ScrollSensitivity;
                        double scrollY = (double)data.deltaY * _settings.ScrollSensitivity;
                        InputSimulator.Scroll((int)scrollY);
                        break;

                    case "key":
                        string key = data.key;
                        InputSimulator.SendKey(key);
                        break;
                }
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"处理消息失败: {ex.Message}");
            }
        }

        private async Task HandleHttpRequest(HttpListenerContext context)
        {
            try
            {
                var request = context.Request;
                var response = context.Response;

                string path = request.Url.AbsolutePath;
                if (path == "/" || path == "/index.html")
                {
                    // 返回嵌入的HTML文件
                    var htmlContent = GetEmbeddedResource("RemoteControl.Desktop.Web.index.html");
                    var buffer = Encoding.UTF8.GetBytes(htmlContent);

                    response.ContentType = "text/html; charset=utf-8";
                    response.ContentLength64 = buffer.Length;
                    await response.OutputStream.WriteAsync(buffer, 0, buffer.Length);
                }
                else
                {
                    response.StatusCode = 404;
                    var notFound = Encoding.UTF8.GetBytes("Not Found");
                    await response.OutputStream.WriteAsync(notFound, 0, notFound.Length);
                }

                response.OutputStream.Close();
            }
            catch (Exception ex)
            {
                OnLog?.Invoke($"HTTP请求处理失败: {ex.Message}");
            }
        }

        private string GetEmbeddedResource(string resourceName)
        {
            var assembly = Assembly.GetExecutingAssembly();
            using var stream = assembly.GetManifestResourceStream(resourceName);
            if (stream == null)
            {
                return "<html><body><h1>Resource not found</h1></body></html>";
            }
            using var reader = new StreamReader(stream);
            return reader.ReadToEnd();
        }
    }
}
