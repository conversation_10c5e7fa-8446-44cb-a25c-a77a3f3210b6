using System;
using System.Diagnostics;
using System.Security.Principal;
using System.Windows;

namespace RemoteControl.Desktop
{
    public partial class App : Application
    {
        [STAThread]
        public static void Main()
        {
            // 检查是否以管理员身份运行
            if (!IsRunAsAdministrator())
            {
                // 重新以管理员身份启动
                RestartAsAdministrator();
                return;
            }

            var app = new App();
            app.InitializeComponent();
            app.Run();
        }

        private static bool IsRunAsAdministrator()
        {
            var identity = WindowsIdentity.GetCurrent();
            var principal = new WindowsPrincipal(identity);
            return principal.IsInRole(WindowsBuiltInRole.Administrator);
        }

        private static void RestartAsAdministrator()
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = Process.GetCurrentProcess().MainModule.FileName,
                    UseShellExecute = true,
                    Verb = "runas" // 以管理员身份运行
                };

                Process.Start(processInfo);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"需要管理员权限才能运行此应用程序。\n\n错误: {ex.Message}",
                    "权限不足", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void InitializeComponent()
        {
            this.StartupUri = new Uri("MainWindow.xaml", UriKind.Relative);
        }
    }
}
